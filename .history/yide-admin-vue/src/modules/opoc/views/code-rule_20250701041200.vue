<template>
  <div class="opoc-code-rule">
    <cl-crud ref="Crud">
      <cl-row>
        <cl-refresh-btn />
        <cl-add-btn />
        <cl-flex1 />
        <cl-search-key placeholder="输入规则名称搜索" />
      </cl-row>

      <cl-row>
        <cl-table ref="Table" />
      </cl-row>

      <cl-row>
        <cl-flex1 />
        <cl-pagination />
      </cl-row>

      <!-- 新增/编辑表单 -->
      <cl-form ref="Form" />

      <!-- 预览表单 -->
      <cl-form ref="PreviewForm" />
    </cl-crud>
  </div>
</template>

<script lang="ts" setup>
import { useCrud, useForm, useTable } from '@cool-vue/crud';
import { ElMessage, ElMessageBox } from 'element-plus';
import { reactive, ref } from 'vue';
import { useCool } from '/@/cool';

// 定义组件名称
defineOptions({
  name: 'opoc-code-rule'
});

const { service } = useCool();

// crud实例
const Crud = useCrud({
  service: service.opoc.codeRule
});

// 表格实例
const Table = useTable({
  columns: [
    { type: 'selection' },
    { label: '规则名称', prop: 'name', minWidth: 120 },
    { label: '规则类型', prop: 'type', minWidth: 100, dict: [
      { label: '随机', value: 'random' },
      { label: '数字', value: 'number' },
      { label: '字母', value: 'letter' },
      { label: '混合', value: 'mixed' },
      { label: '自定义', value: 'custom' }
    ]},
    { label: '规则前缀', prop: 'prefix', minWidth: 100 },
    { label: '规则长度', prop: 'length', minWidth: 100 },
    { label: '包含字母', prop: 'includeLetters', minWidth: 100, dict: [
      { label: '是', value: true },
      { label: '否', value: false }
    ]},
    { label: '包含数字', prop: 'includeNumbers', minWidth: 100, dict: [
      { label: '是', value: true },
      { label: '否', value: false }
    ]},
    { label: '包含特殊字符', prop: 'includeSpecialChars', minWidth: 100, dict: [
      { label: '是', value: true },
      { label: '否', value: false }
    ]},
    { label: '是否默认', prop: 'isDefault', minWidth: 100, dict: [
      { label: '是', value: true },
      { label: '否', value: false }
    ]},
    { label: '状态', prop: 'status', minWidth: 80, dict: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ]},
    { label: '创建时间', prop: 'createTime', minWidth: 150 },
    {
      label: '操作',
      type: 'op',
      buttons: ['edit', 'delete', {
        label: '设为默认',
        hidden: ({ isDefault }) => isDefault,
        onClick: ({ id }) => {
          setDefault(id);
        }
      }, {
        label: '预览',
        onClick: ({ id, name, type, length, prefix, includeLetters, includeNumbers, includeSpecialChars }) => {
          previewCode({ id, name, type, length, prefix, includeLetters, includeNumbers, includeSpecialChars });
        }
      }]
    }
  ]
});

// 表单实例
const Form = useForm({
  items: [
    { label: '规则名称', prop: 'name', required: true, component: { name: 'el-input', props: { placeholder: '请输入规则名称' } } },
    { label: '规则类型', prop: 'type', required: true, component: { 
      name: 'el-select', 
      props: { placeholder: '请选择规则类型' },
      options: [
        { label: '随机', value: 'random' },
        { label: '数字', value: 'number' },
        { label: '字母', value: 'letter' },
        { label: '混合', value: 'mixed' },
        { label: '自定义', value: 'custom' }
      ]
    }},
    { label: '规则前缀', prop: 'prefix', component: { name: 'el-input', props: { placeholder: '请输入规则前缀' } } },
    { label: '规则长度', prop: 'length', required: true, value: 8, component: { 
      name: 'el-input-number', 
      props: { min: 4, max: 32, placeholder: '请输入规则长度' } 
    }},
    { label: '包含字母', prop: 'includeLetters', value: true, component: { 
      name: 'el-switch',
      vIf: ({ type }) => type === 'custom'
    }},
    { label: '包含数字', prop: 'includeNumbers', value: true, component: { 
      name: 'el-switch',
      vIf: ({ type }) => type === 'custom'
    }},
    { label: '包含特殊字符', prop: 'includeSpecialChars', value: false, component: { 
      name: 'el-switch',
      vIf: ({ type }) => type === 'custom'
    }},
    { label: '规则描述', prop: 'description', component: { name: 'el-input', props: { type: 'textarea', rows: 3, placeholder: '请输入规则描述' } } },
    { label: '状态', prop: 'status', value: 1, component: { 
      name: 'el-radio-group',
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ]
    }}
  ]
});

// 预览表单实例
const PreviewForm = useForm({
  title: '预览编码规则',
  width: '500px',
  items: [
    { label: '规则名称', prop: 'name', component: { name: 'el-input', props: { disabled: true } } },
    { label: '规则类型', prop: 'type', component: { name: 'el-input', props: { disabled: true } } },
    { label: '规则长度', prop: 'length', component: { name: 'el-input', props: { disabled: true } } },
    { label: '预览码', prop: 'previewCode', component: { name: 'el-input', props: { disabled: true } } }
  ],
  on: {
    submit: (data, { done, close }) => {
      close();
    }
  }
});

// 设置默认规则
async function setDefault(id) {
  try {
    await ElMessageBox.confirm('确定要将此规则设为默认规则吗？', '提示', {
      type: 'warning',
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    });

    await service.opoc.codeRule.setDefault({ id });
    ElMessage.success('设置成功');
    Crud.value?.refresh();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '设置失败');
    }
  }
}

// 预览编码
async function previewCode(rule) {
  try {
    const res = await service.opoc.codeRule.preview(rule);
    
    PreviewForm.value?.open({
      title: '预览编码规则',
      form: {
        ...rule,
        previewCode: res.code
      }
    });
  } catch (error) {
    ElMessage.error(error.message || '预览失败');
  }
}
</script>

<style lang="scss" scoped>
.opoc-code-rule {
  display: flex;
  flex-direction: column;
  height: 100%;
}
</style>
