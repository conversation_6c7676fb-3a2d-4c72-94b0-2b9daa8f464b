import { createApp, close, createHttpRequest } from '@midwayjs/mock';
import { Framework } from '@midwayjs/koa';
import * as assert from 'assert';
import { BarcodeUtil } from '../../src/modules/opoc/util/barcode';

describe('test/util/barcode.test.ts', () => {
  let app: any;
  let barcodeUtil: BarcodeUtil;

  beforeAll(async () => {
    // 创建应用
    app = await createApp<Framework>();
    // 获取条形码工具
    barcodeUtil = await app.getApplicationContext().getAsync(BarcodeUtil);
  });

  afterAll(async () => {
    // 关闭应用
    await close(app);
  });

  // 测试基本条形码生成
  it('应该能够生成基本条形码', () => {
    const text = 'TEST123456';
    const result = barcodeUtil.generate(text);
    
    // 断言结果
    assert(result);
    assert(typeof result === 'string');
    assert(result.startsWith('data:image/png;base64,'));
    
    console.log('生成的条形码长度:', result.length);
  });

  // 测试生成Buffer格式条形码
  it('应该能够生成Buffer格式条形码', () => {
    const text = 'BUFFER123';
    const result = barcodeUtil.generateBuffer(text);
    
    // 断言结果
    assert(result);
    assert(Buffer.isBuffer(result));
    assert(result.length > 0);
    
    console.log('生成的Buffer大小:', result.length, 'bytes');
  });

  // 测试EAN-13条形码生成
  it('应该能够生成EAN-13条形码', () => {
    const text = '1234567890123'; // 13位数字
    const result = barcodeUtil.generateEAN13(text);
    
    // 断言结果
    assert(result);
    assert(typeof result === 'string');
    assert(result.startsWith('data:image/png;base64,'));
  });

  // 测试EAN-13条形码验证
  it('应该验证EAN-13条形码输入格式', () => {
    const invalidText = '12345'; // 不是13位数字
    
    try {
      barcodeUtil.generateEAN13(invalidText);
      assert.fail('应该抛出错误');
    } catch (error) {
      assert(error.message.includes('EAN-13条形码必须是13位数字'));
    }
  });

  // 测试UPC-A条形码生成
  it('应该能够生成UPC-A条形码', () => {
    const text = '123456789012'; // 12位数字
    const result = barcodeUtil.generateUPCA(text);
    
    // 断言结果
    assert(result);
    assert(typeof result === 'string');
    assert(result.startsWith('data:image/png;base64,'));
  });

  // 测试CODE39条形码生成
  it('应该能够生成CODE39条形码', () => {
    const text = 'CODE39TEST';
    const result = barcodeUtil.generateCODE39(text);
    
    // 断言结果
    assert(result);
    assert(typeof result === 'string');
    assert(result.startsWith('data:image/png;base64,'));
  });

  // 测试批量生成条形码
  it('应该能够批量生成条形码', () => {
    const textList = ['BATCH001', 'BATCH002', 'BATCH003'];
    const results = barcodeUtil.batchGenerate(textList);
    
    // 断言结果
    assert(results);
    assert(Array.isArray(results));
    assert.equal(results.length, 3);
    
    results.forEach(result => {
      assert(typeof result === 'string');
      assert(result.startsWith('data:image/png;base64,'));
    });
  });

  // 测试自定义选项
  it('应该能够使用自定义选项生成条形码', () => {
    const text = 'CUSTOM123';
    const options = {
      width: 3,
      height: 80,
      background: '#f0f0f0',
      lineColor: '#333333'
    };
    
    const result = barcodeUtil.generate(text, options);
    
    // 断言结果
    assert(result);
    assert(typeof result === 'string');
    assert(result.startsWith('data:image/png;base64,'));
  });
});
