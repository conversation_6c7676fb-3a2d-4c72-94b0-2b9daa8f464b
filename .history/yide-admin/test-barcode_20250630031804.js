// 简单的条形码测试脚本
const { BarcodeUtil } = require('./dist/modules/opoc/util/barcode');

async function testBarcode() {
  try {
    console.log('开始测试条形码生成功能...');
    
    const barcodeUtil = new BarcodeUtil();
    
    // 测试基本条形码生成
    console.log('1. 测试基本条形码生成...');
    const result1 = barcodeUtil.generate('123456789');
    console.log('✓ 基本条形码生成成功，长度:', result1.length);
    console.log('✓ 数据格式:', result1.substring(0, 50) + '...');
    
    // 测试Buffer格式
    console.log('2. 测试Buffer格式生成...');
    const buffer = barcodeUtil.generateBuffer('123456789');
    console.log('✓ Buffer格式生成成功，大小:', buffer.length, 'bytes');
    
    // 测试自定义选项
    console.log('3. 测试自定义选项...');
    const result3 = barcodeUtil.generate('HELLO123', {
      width: 3,
      height: 120,
      background: '#f0f0f0',
      lineColor: '#333333'
    });
    console.log('✓ 自定义选项生成成功');
    
    // 测试EAN-13
    console.log('4. 测试EAN-13格式...');
    const ean13 = barcodeUtil.generateEAN13('1234567890123');
    console.log('✓ EAN-13生成成功');
    
    // 测试批量生成
    console.log('5. 测试批量生成...');
    const batchResults = barcodeUtil.batchGenerate(['123', '456', '789']);
    console.log('✓ 批量生成成功，数量:', batchResults.length);
    
    console.log('\n🎉 所有测试通过！条形码功能已修复！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error(error.stack);
  }
}

testBarcode();
