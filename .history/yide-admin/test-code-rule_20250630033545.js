// 简单的编码规则功能测试
const { OpocCodeRuleService } = require('./src/modules/opoc/service/code-rule');

// 模拟编码规则实体
class MockCodeRuleEntity {
  constructor(data) {
    Object.assign(this, data);
  }
}

// 创建编码规则服务实例
const codeRuleService = new OpocCodeRuleService();

// 模拟数据库查询方法
codeRuleService.opocProductCodeEntity = {
  count: async () => 0 // 模拟没有重复编码
};

console.log('开始测试编码规则功能...');

// 测试1: 生成预览码
async function testGeneratePreview() {
  console.log('\n1. 测试生成预览码...');
  
  const params = {
    type: 'mixed',
    length: 8,
    prefix: 'TEST',
    includeLetters: true,
    includeNumbers: true,
    includeSpecialChars: false
  };

  try {
    const result = await codeRuleService.generatePreview(params);
    console.log('✓ 预览码生成成功:', result.code);
    console.log('✓ 前缀检查:', result.code.startsWith('TEST') ? '通过' : '失败');
    console.log('✓ 长度检查:', result.code.length === 12 ? '通过' : '失败');
  } catch (error) {
    console.log('✗ 预览码生成失败:', error.message);
  }
}

// 测试2: 生成唯一编码
async function testGenerateUniqueCode() {
  console.log('\n2. 测试生成唯一编码...');
  
  const testRule = new MockCodeRuleEntity({
    name: '测试规则',
    type: 'mixed',
    length: 6,
    prefix: 'UT',
    includeLetters: true,
    includeNumbers: true,
    includeSpecialChars: false
  });

  try {
    const codes = await codeRuleService.generateUniqueCode(testRule, 3);
    console.log('✓ 唯一编码生成成功:', codes);
    console.log('✓ 数量检查:', codes.length === 3 ? '通过' : '失败');
    console.log('✓ 前缀检查:', codes.every(code => code.startsWith('UT')) ? '通过' : '失败');
    console.log('✓ 长度检查:', codes.every(code => code.length === 8) ? '通过' : '失败');
    
    // 检查唯一性
    const uniqueCodes = new Set(codes);
    console.log('✓ 唯一性检查:', uniqueCodes.size === codes.length ? '通过' : '失败');
  } catch (error) {
    console.log('✗ 唯一编码生成失败:', error.message);
  }
}

// 测试3: 高级编码生成
async function testGenerateAdvancedCode() {
  console.log('\n3. 测试高级编码生成...');
  
  const testRule = new MockCodeRuleEntity({
    name: '高级测试规则',
    type: 'mixed',
    length: 10,
    prefix: 'ADV',
    includeLetters: true,
    includeNumbers: true,
    includeSpecialChars: false
  });

  const options = {
    includeTimestamp: true,
    timestampFormat: 'YYMMDD',
    includeSequence: true,
    sequenceLength: 4,
    includeChecksum: true
  };

  try {
    const code = await codeRuleService.generateAdvancedCode(testRule, options);
    console.log('✓ 高级编码生成成功:', code);
    console.log('✓ 前缀检查:', code.startsWith('ADV') ? '通过' : '失败');
  } catch (error) {
    console.log('✗ 高级编码生成失败:', error.message);
  }
}

// 测试4: 编码格式验证
function testValidateCodeFormat() {
  console.log('\n4. 测试编码格式验证...');
  
  const testRule = new MockCodeRuleEntity({
    name: '验证测试规则',
    type: 'number',
    length: 8,
    prefix: 'NUM',
    includeLetters: false,
    includeNumbers: true,
    includeSpecialChars: false
  });

  // 测试有效编码
  const validCode = 'NUM12345678';
  const isValid = codeRuleService.validateCodeFormat(validCode, testRule);
  console.log('✓ 有效编码验证:', isValid ? '通过' : '失败');

  // 测试无效编码（包含字母）
  const invalidCode = 'NUMABC12345';
  const isInvalid = codeRuleService.validateCodeFormat(invalidCode, testRule);
  console.log('✓ 无效编码验证:', !isInvalid ? '通过' : '失败');

  // 测试错误前缀
  const wrongPrefix = 'ABC12345678';
  const isWrongPrefix = codeRuleService.validateCodeFormat(wrongPrefix, testRule);
  console.log('✓ 错误前缀验证:', !isWrongPrefix ? '通过' : '失败');
}

// 测试5: 自定义编码格式验证
function testValidateCustomFormat() {
  console.log('\n5. 测试自定义编码格式验证...');
  
  const testRule = new MockCodeRuleEntity({
    name: '自定义测试规则',
    type: 'custom',
    length: 6,
    prefix: 'CUS',
    includeLetters: true,
    includeNumbers: true,
    includeSpecialChars: false
  });

  // 测试有效编码（字母+数字）
  const validCode = 'CUSABC123';
  const isValid = codeRuleService.validateCodeFormat(validCode, testRule);
  console.log('✓ 自定义有效编码验证:', isValid ? '通过' : '失败');

  // 测试无效编码（包含特殊字符）
  const invalidCode = 'CUSABC@#$';
  const isInvalid = codeRuleService.validateCodeFormat(invalidCode, testRule);
  console.log('✓ 自定义无效编码验证:', !isInvalid ? '通过' : '失败');
}

// 运行所有测试
async function runAllTests() {
  try {
    await testGeneratePreview();
    await testGenerateUniqueCode();
    await testGenerateAdvancedCode();
    testValidateCodeFormat();
    testValidateCustomFormat();
    
    console.log('\n🎉 所有编码规则测试完成！');
  } catch (error) {
    console.error('测试过程中出现错误:', error);
  }
}

runAllTests();
