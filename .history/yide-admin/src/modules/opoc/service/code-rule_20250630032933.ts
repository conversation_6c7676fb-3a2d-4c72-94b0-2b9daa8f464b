import { BaseService, CoolCommException } from '@cool-midway/core';
import { Provide } from '@midwayjs/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { OpocCodeRuleEntity } from '../entity/code-rule';

/**
 * 编码规则服务
 */
@Provide()
export class OpocCodeRuleService extends BaseService {
  @InjectEntityModel(OpocCodeRuleEntity)
  opocCodeRuleEntity: Repository<OpocCodeRuleEntity>;

  @InjectEntityModel(OpocProductCodeEntity)
  opocProductCodeEntity: Repository<OpocProductCodeEntity>;

  /**
   * 设置默认规则
   * @param id 规则ID
   */
  async setDefault(id: number) {
    // 查询规则是否存在
    const rule = await this.opocCodeRuleEntity.findOne({ where: { id } });
    if (!rule) {
      throw new CoolCommException('规则不存在');
    }

    // 将所有规则设置为非默认
    await this.opocCodeRuleEntity.update({}, { isDefault: false });

    // 将指定规则设置为默认
    await this.opocCodeRuleEntity.update(id, { isDefault: true });

    return true;
  }

  /**
   * 生成预览码
   * @param params 码格式参数
   */
  async generatePreview(params: {
    type: string;
    length: number;
    includeLetters?: boolean;
    includeNumbers?: boolean;
    includeSpecialChars?: boolean;
    prefix?: string;
  }) {
    let code = '';
    const prefix = params.prefix || '';
    
    // 根据类型生成不同的码
    switch (params.type) {
      case 'random':
        code = this.generateRandomCode(params.length);
        break;
      case 'number':
        code = this.generateNumberCode(params.length);
        break;
      case 'letter':
        code = this.generateLetterCode(params.length);
        break;
      case 'mixed':
        code = this.generateMixedCode(params.length);
        break;
      case 'custom':
        code = this.generateCustomCode(
          params.length,
          params.includeLetters || false,
          params.includeNumbers || false,
          params.includeSpecialChars || false
        );
        break;
      default:
        code = this.generateMixedCode(params.length);
    }
    
    return {
      code: prefix + code,
      format: params
    };
  }

  /**
   * 生成随机字符码
   */
  private generateRandomCode(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+';
    return this.generateCodeFromChars(chars, length);
  }

  /**
   * 生成数字码
   */
  private generateNumberCode(length: number): string {
    const chars = '0123456789';
    return this.generateCodeFromChars(chars, length);
  }

  /**
   * 生成字母码
   */
  private generateLetterCode(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    return this.generateCodeFromChars(chars, length);
  }

  /**
   * 生成混合码（字母+数字）
   */
  private generateMixedCode(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    return this.generateCodeFromChars(chars, length);
  }

  /**
   * 生成自定义码
   */
  private generateCustomCode(
    length: number,
    includeLetters: boolean,
    includeNumbers: boolean,
    includeSpecialChars: boolean
  ): string {
    let chars = '';
    
    if (includeLetters) {
      chars += 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    }
    
    if (includeNumbers) {
      chars += '0123456789';
    }
    
    if (includeSpecialChars) {
      chars += '!@#$%^&*()_+-=[]{}|;:,.<>?';
    }
    
    // 如果没有选择任何字符类型，默认使用字母+数字
    if (!chars) {
      chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    }
    
    return this.generateCodeFromChars(chars, length);
  }

  /**
   * 从字符集生成指定长度的随机码
   */
  private generateCodeFromChars(chars: string, length: number): string {
    let result = '';
    const charsLength = chars.length;

    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * charsLength));
    }

    return result;
  }

  /**
   * 获取默认编码规则
   */
  async getDefaultRule(): Promise<OpocCodeRuleEntity | null> {
    return await this.opocCodeRuleEntity.findOne({
      where: { isDefault: true, status: 1 }
    });
  }

  /**
   * 根据规则生成唯一编码
   * @param rule 编码规则
   * @param count 生成数量（用于批量生成时确保唯一性）
   */
  async generateUniqueCode(rule: OpocCodeRuleEntity, count: number = 1): Promise<string[]> {
    const codes: string[] = [];
    const maxAttempts = count * 10; // 最大尝试次数，避免无限循环
    let attempts = 0;

    while (codes.length < count && attempts < maxAttempts) {
      attempts++;

      // 生成基础编码
      let baseCode = '';
      switch (rule.type) {
        case 'random':
          baseCode = this.generateRandomCode(rule.length);
          break;
        case 'number':
          baseCode = this.generateNumberCode(rule.length);
          break;
        case 'letter':
          baseCode = this.generateLetterCode(rule.length);
          break;
        case 'mixed':
          baseCode = this.generateMixedCode(rule.length);
          break;
        case 'custom':
          baseCode = this.generateCustomCode(
            rule.length,
            rule.includeLetters,
            rule.includeNumbers,
            rule.includeSpecialChars
          );
          break;
        default:
          baseCode = this.generateMixedCode(rule.length);
      }

      // 添加前缀
      const fullCode = (rule.prefix || '') + baseCode;

      // 检查编码是否已存在
      const exists = await this.checkCodeExists(fullCode);
      if (!exists && !codes.includes(fullCode)) {
        codes.push(fullCode);
      }
    }

    if (codes.length < count) {
      throw new CoolCommException(`无法生成足够的唯一编码，仅生成了 ${codes.length}/${count} 个`);
    }

    return codes;
  }

  /**
   * 检查编码是否已存在
   */
  private async checkCodeExists(code: string): Promise<boolean> {
    const count = await this.opocProductCodeEntity.count({ where: { code } });
    return count > 0;
  }

  /**
   * 生成高级编码（包含时间戳、序列号等）
   * @param rule 编码规则
   * @param options 高级选项
   */
  async generateAdvancedCode(rule: OpocCodeRuleEntity, options: {
    includeTimestamp?: boolean;
    timestampFormat?: 'YYYYMMDD' | 'YYMMDD' | 'YYYYMMDDHHMM';
    includeSequence?: boolean;
    sequenceLength?: number;
    includeChecksum?: boolean;
  } = {}): Promise<string> {
    let code = rule.prefix || '';

    // 添加时间戳
    if (options.includeTimestamp) {
      const now = new Date();
      switch (options.timestampFormat) {
        case 'YYYYMMDD':
          code += now.getFullYear().toString() +
                  (now.getMonth() + 1).toString().padStart(2, '0') +
                  now.getDate().toString().padStart(2, '0');
          break;
        case 'YYMMDD':
          code += (now.getFullYear() % 100).toString().padStart(2, '0') +
                  (now.getMonth() + 1).toString().padStart(2, '0') +
                  now.getDate().toString().padStart(2, '0');
          break;
        case 'YYYYMMDDHHMM':
          code += now.getFullYear().toString() +
                  (now.getMonth() + 1).toString().padStart(2, '0') +
                  now.getDate().toString().padStart(2, '0') +
                  now.getHours().toString().padStart(2, '0') +
                  now.getMinutes().toString().padStart(2, '0');
          break;
      }
    }

    // 添加序列号
    if (options.includeSequence) {
      const sequenceLength = options.sequenceLength || 4;
      const sequence = await this.getNextSequence();
      code += sequence.toString().padStart(sequenceLength, '0');
    }

    // 生成剩余长度的随机码
    const remainingLength = rule.length - (code.length - (rule.prefix?.length || 0));
    if (remainingLength > 0) {
      let randomPart = '';
      switch (rule.type) {
        case 'number':
          randomPart = this.generateNumberCode(remainingLength);
          break;
        case 'letter':
          randomPart = this.generateLetterCode(remainingLength);
          break;
        case 'mixed':
          randomPart = this.generateMixedCode(remainingLength);
          break;
        case 'custom':
          randomPart = this.generateCustomCode(
            remainingLength,
            rule.includeLetters,
            rule.includeNumbers,
            rule.includeSpecialChars
          );
          break;
        default:
          randomPart = this.generateMixedCode(remainingLength);
      }
      code += randomPart;
    }

    // 添加校验位
    if (options.includeChecksum) {
      const checksum = this.calculateChecksum(code);
      code += checksum;
    }

    return code;
  }

  /**
   * 获取下一个序列号
   */
  private async getNextSequence(): Promise<number> {
    // 这里可以实现序列号生成逻辑，比如从数据库获取或使用Redis
    // 简单实现：基于当前时间戳的毫秒数
    return Date.now() % 10000;
  }

  /**
   * 计算校验位（简单的模10算法）
   */
  private calculateChecksum(code: string): string {
    let sum = 0;
    for (let i = 0; i < code.length; i++) {
      const char = code.charAt(i);
      if (/\d/.test(char)) {
        sum += parseInt(char);
      } else {
        // 字母转换为数字（A=1, B=2, ...）
        sum += char.toUpperCase().charCodeAt(0) - 64;
      }
    }
    return (sum % 10).toString();
  }

  /**
   * 验证编码格式是否符合规则
   * @param code 编码
   * @param rule 编码规则
   */
  validateCodeFormat(code: string, rule: OpocCodeRuleEntity): boolean {
    // 检查前缀
    if (rule.prefix && !code.startsWith(rule.prefix)) {
      return false;
    }

    // 检查长度（不包含前缀）
    const codeWithoutPrefix = rule.prefix ? code.substring(rule.prefix.length) : code;
    if (codeWithoutPrefix.length !== rule.length) {
      return false;
    }

    // 检查字符类型
    switch (rule.type) {
      case 'number':
        return /^\d+$/.test(codeWithoutPrefix);
      case 'letter':
        return /^[A-Za-z]+$/.test(codeWithoutPrefix);
      case 'mixed':
        return /^[A-Za-z0-9]+$/.test(codeWithoutPrefix);
      case 'custom':
        return this.validateCustomFormat(codeWithoutPrefix, rule);
      default:
        return true;
    }
  }

  /**
   * 验证自定义格式
   */
  private validateCustomFormat(code: string, rule: OpocCodeRuleEntity): boolean {
    let allowedChars = '';

    if (rule.includeLetters) {
      allowedChars += 'A-Za-z';
    }

    if (rule.includeNumbers) {
      allowedChars += '0-9';
    }

    if (rule.includeSpecialChars) {
      allowedChars += '!@#$%^&*()_+\\-=\\[\\]{}|;:,.<>?';
    }

    if (!allowedChars) {
      return false;
    }

    const regex = new RegExp(`^[${allowedChars}]+$`);
    return regex.test(code);
  }

  /**
   * 获取编码规则使用统计
   * @param ruleId 规则ID
   */
  async getRuleUsageStats(ruleId: number) {
    const rule = await this.opocCodeRuleEntity.findOne({ where: { id: ruleId } });
    if (!rule) {
      throw new CoolCommException('编码规则不存在');
    }

    // 统计使用该规则生成的编码数量
    const totalCodes = await this.opocProductCodeEntity.count({
      where: {
        code: rule.prefix ? `${rule.prefix}%` : undefined
      }
    });

    // 统计各状态的编码数量
    const statusStats = await this.nativeQuery(`
      SELECT status, COUNT(*) as count
      FROM opoc_product_code
      WHERE code LIKE ?
      GROUP BY status
    `, [rule.prefix ? `${rule.prefix}%` : '%']);

    return {
      rule,
      totalCodes,
      statusStats: statusStats.reduce((acc, item) => {
        acc[item.status] = item.count;
        return acc;
      }, {}),
      usageRate: totalCodes > 0 ? ((statusStats.find(s => s.status === 1)?.count || 0) / totalCodes * 100).toFixed(2) : 0
    };
  }
}
