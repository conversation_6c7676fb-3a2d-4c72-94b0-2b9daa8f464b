import { BaseService } from '@cool-midway/core'
import { Provide } from '@midwayjs/core'
import { InjectEntityModel } from '@midwayjs/typeorm'
import { Between, In, Repository } from 'typeorm'
import { OpocProductCodeEntity } from '../entity/product-code'
import { OpocProductInfoEntity } from '../entity/product-info'
import { OpocVerificationLogEntity } from '../entity/verification-log'
import { ExportParams, GenerateCodeParams, SetVerifyLimitParams, VerifyCodeParams } from '../typings/opoc'

/**
 * 产品码服务
 */
@Provide()
export class OpocProductCodeService extends BaseService {
  @InjectEntityModel(OpocProductCodeEntity)
  opocProductCodeEntity: Repository<OpocProductCodeEntity>;

  @InjectEntityModel(OpocProductInfoEntity)
  opocProductInfoEntity: Repository<OpocProductInfoEntity>;

  @InjectEntityModel(OpocVerificationLogEntity)
  opocVerificationLogEntity: Repository<OpocVerificationLogEntity>;

  @InjectEntityModel(OpocCodeRuleEntity)
  opocCodeRuleEntity: Repository<OpocCodeRuleEntity>;

  @Inject()
  opocCodeRuleService: OpocCodeRuleService;

  /**
   * 分页查询
   * @param query 查询条件
   * @returns
   */
  async page(query) {
    const sql = `
      SELECT a.*, b.productName, b.productCode, b.skuCode, b.skuName, b.image,
             b.batchNo, b.produceDate, b.expireDate, b.status as productStatus
      FROM opoc_product_code a
      LEFT JOIN opoc_product_info b ON a.productId = b.id
      WHERE 1 = 1
      ${this.setSql(query.keyWord, 'AND (a.code LIKE ?)', [`%${query.keyWord}%`])}
      ${this.setSql(query.status, 'AND a.status = ?', [query.status])}
      ${this.setSql(query.productId, 'AND a.productId = ?', [query.productId])}
    `;
    return this.sqlRenderPage(sql, query);
  }

  /**
   * 列表查询
   * @param query 查询条件
   * @returns
   */
  async list(query) {
    const sql = `
      SELECT a.*, b.productName, b.productCode, b.skuCode, b.skuName, b.image,
             b.batchNo, b.produceDate, b.expireDate, b.status as productStatus
      FROM opoc_product_code a
      LEFT JOIN opoc_product_info b ON a.productId = b.id
      WHERE 1 = 1
      ${this.setSql(query.keyWord, 'AND (a.code LIKE ?)', [`%${query.keyWord}%`])}
      ${this.setSql(query.status, 'AND a.status = ?', [query.status])}
      ${this.setSql(query.productId, 'AND a.productId = ?', [query.productId])}
    `;
    return this.nativeQuery(sql);
  }

  /**
   * 信息查询
   * @param id ID
   * @returns
   */
  async info(id) {
    const sql = `
      SELECT a.*, b.productName, b.productCode, b.skuCode, b.skuName, b.image,
             b.batchNo, b.produceDate, b.expireDate, b.status as productStatus
      FROM opoc_product_code a
      LEFT JOIN opoc_product_info b ON a.productId = b.id
      WHERE a.id = ?
    `;
    const result = await this.nativeQuery(sql, [id]);
    return result?.[0];
  }

  /**
   * 生成产品码
   * @param params 生成参数
   * @returns
   */
  async generate(params: GenerateCodeParams) {
    let rule: OpocCodeRuleEntity;

    // 如果指定了规则ID，使用指定规则
    if (params.ruleId) {
      rule = await this.opocCodeRuleEntity.findOne({ where: { id: params.ruleId } });
      if (!rule) {
        throw new Error('指定的编码规则不存在');
      }
    } else {
      // 否则使用默认规则
      rule = await this.opocCodeRuleService.getDefaultRule();
      if (!rule) {
        throw new Error('未找到默认编码规则，请先配置编码规则');
      }
    }

    // 使用编码规则生成唯一编码
    const generatedCodes = await this.opocCodeRuleService.generateUniqueCode(rule, params.quantity);

    // 创建产品码实体
    const codes = generatedCodes.map(code => ({
      code,
      status: 0, // 未使用
      ruleId: rule.id, // 记录使用的规则ID
      createTime: new Date(),
      updateTime: new Date()
    }));

    return await this.opocProductCodeEntity.save(codes);
  }

  /**
   * 生成唯一标识码
   */
  private generateUniqueCode(params: GenerateCodeParams) {
    // 这里可以实现自己的编码生成规则，如时间戳+随机数
    let code = '';
    if (params.type === 'random') {
      code = Math.random().toString(36).substring(2, 2 + params.length);
    } else if (params.type === 'number') {
      code = Math.floor(Math.random() * 10000).toString().padStart(params.length, '0');
    } else if (params.type === 'letter') {
      code = Math.random().toString(36).substring(2, 2 + params.length);
    } else if (params.type === 'mixed') {
      code = Math.random().toString(36).substring(2, 2 + params.length);
    } else {
      throw new Error('无效的产品码类型');
    }
    return code;
  }

  /**
   * 绑定产品码
   * @param codeId 产品码ID
   * @param productId 产品ID
   */
  async bind(codeId: number, productId: number) {
    const code = await this.opocProductCodeEntity.findOneBy({ id: codeId });
    if (!code) {
      throw new Error('产品码不存在');
    }
    if (code.status !== 0) {
      throw new Error('产品码已被使用');
    }

    return await this.opocProductCodeEntity.update(codeId, {
      productId,
      status: 1, // 已绑定
    });
  }

  /**
   * 批量绑定产品码
   * @param params 批量绑定参数
   * @returns 绑定结果
   */
  async batchBind(params: { codeIds: number[]; productId: number }) {
    const { codeIds, productId } = params;

    if (!codeIds || codeIds.length === 0) {
      throw new Error('请选择产品码');
    }

    if (!productId) {
      throw new Error('请选择产品');
    }

    // 检查产品是否存在
    const product = await this.opocProductInfoEntity.findOneBy({ id: productId });
    if (!product) {
      throw new Error('产品不存在');
    }

    // 查询所有产品码
    const codes = await this.opocProductCodeEntity.find({
      where: { id: In(codeIds) }
    });

    if (codes.length === 0) {
      throw new Error('未找到有效的产品码');
    }

    // 检查产品码状态
    const invalidCodes = codes.filter(code => code.status !== 0);
    if (invalidCodes.length > 0) {
      return {
        success: false,
        message: '存在已被使用的产品码',
        invalidCodes: invalidCodes.map(code => code.code)
      };
    }

    // 批量更新产品码
    const result = await this.opocProductCodeEntity.update(
      { id: In(codeIds) },
      {
        productId,
        status: 1 // 已绑定
      }
    );

    return {
      success: true,
      message: `成功绑定${result.affected}个产品码`,
      affected: result.affected
    };
  }

  /**
   * 激活产品码
   * @param codeId 产品码ID
   */
  async activate(id: number) {
    const code = await this.opocProductCodeEntity.findOneBy({ id });
    if (!code) {
      throw new Error('产品码不存在');
    }
    if (code.status !== 1) {
      throw new Error('产品码未绑定或已失效');
    }

    return await this.opocProductCodeEntity.update(id, {
      status: 2, // 已激活
      activateTime: new Date() // 设置当前时间为激活时间
    });
  }

  /**
   * 验证产品码
   * @param code 产品码
   * @returns 产品信息
   */
  async verifyCode(params: VerifyCodeParams) {
    const { code, location } = params;
    // 查询产品码
    const productCode = await this.opocProductCodeEntity.findOneBy({ code });
    // 产品码不存在
    if (!productCode) {
      await this.recordVerifyLog(0, code, 1, location); // 记录为假冒产品
      return {
        verified: false,
        message: '产品码不存在'
      }
    }

    // 产品码未绑定产品
    if (productCode.status === 0) {
      await this.recordVerifyLog(productCode.id, code, 1, location); // 记录为假冒产品
      return {
        verified: false,
        message: '产品码未绑定产品'
      }
    }

    // 检查验证次数限制
    if (productCode.verifyLimit > 0 && productCode.verifyCount >= productCode.verifyLimit) {
      await this.recordVerifyLog(productCode.id, code, 1, location); // 记录为验证失败
      return {
        verified: false,
        message: '产品码验证次数已达上限'
      }
    }

    // 查询产品详细信息
    const sql = `
      SELECT a.*, b.productName, b.productCode, b.skuCode, b.skuName, b.image,
             b.batchNo, b.produceDate, b.expireDate, b.status as productStatus
      FROM opoc_product_code a
      LEFT JOIN opoc_product_info b ON a.productId = b.id
      WHERE a.code = ?
    `;

    const result = await this.nativeQuery(sql, [code]);
    if (!result?.length) {
      await this.recordVerifyLog(productCode.id, code, 1, location); // 记录为假冒产品
      return {
        verified: false,
        message: '获取产品信息失败'
      }
    }

    // 记录验证日志
    await this.recordVerifyLog(productCode.id, code, 0, location); // 记录为真品

    // 更新验证次数和时间
    await this.opocProductCodeEntity.update(productCode.id, {
      verifyCount: () => 'IFNULL(verifyCount, 0) + 1',
      lastVerifyTime: new Date()
    });

    return {
      ...result[0],
      verified: true,
      verifyTime: new Date()
    };
  }

  /**
   * 设置验证次数限制
   * @param params 参数
   * @returns
   */
  async setVerifyLimit(params: SetVerifyLimitParams) {
    const { id, verifyLimit } = params;

    // 查询产品码
    const productCode = await this.opocProductCodeEntity.findOneBy({ id });
    if (!productCode) {
      throw new Error('产品码不存在');
    }

    // 更新验证次数限制
    return await this.opocProductCodeEntity.update(id, {
      verifyLimit
    });
  }

  /**
   * 批量设置验证次数限制
   * @param ids 产品码ID数组
   * @param verifyLimit 验证次数限制
   * @returns
   */
  async batchSetVerifyLimit(ids: number[], verifyLimit: number) {
    if (!ids || !ids.length) {
      throw new Error('请选择产品码');
    }

    // 批量更新验证次数限制
    return await this.opocProductCodeEntity.update(ids, {
      verifyLimit
    });
  }

  /**
   * 记录验证日志
   * @param codeId 产品码ID
   * @param code 产品码
   * @param verifyResult 验证结果（0:真, 1:假）
   * @param location 地理位置
   */
  private async recordVerifyLog(codeId: number, code: string, verifyResult: number, location?: {address: string, longitude: number, latitude: number}) {
    // 记录到验证日志表
    await this.opocVerificationLogEntity.save({
      codeId,
      verifyTime: new Date(),
      verifyResult,
      verifyLocation: location?.address || '',
      longitude: location?.longitude || 0,
      latitude: location?.latitude || 0
    });

    console.log(`产品码 ${code} 被验证，结果: ${verifyResult === 0 ? '真品' : '假冒'}, 时间：${new Date()}`);
  }

  /**
   * 批量导出产品码
   * @param params 导出参数
   * @returns 导出数据
   */
  async exportCodes(params: ExportParams) {
    const { ids, fields, status, productId, dateRange } = params;

    // 构建查询条件
    const where: any = {};

    // 如果指定了ID列表，则按ID查询
    if (ids && ids.length > 0) {
      where.id = In(ids);
    }

    // 如果指定了状态，则按状态查询
    if (status !== undefined) {
      where.status = status;
    }

    // 如果指定了产品ID，则按产品ID查询
    if (productId) {
      where.productId = productId;
    }

    // 如果指定了日期范围，则按创建时间查询
    if (dateRange && dateRange.length === 2) {
      where.createTime = Between(dateRange[0], dateRange[1]);
    }

    // 查询数据
    const sql = `
      SELECT a.*, b.productName, b.productCode, b.skuCode, b.skuName, b.image,
             b.batchNo, b.produceDate, b.expireDate, b.status as productStatus
      FROM opoc_product_code a
      LEFT JOIN opoc_product_info b ON a.productId = b.id
      WHERE 1 = 1
      ${Object.keys(where).length > 0 ? 'AND ' + Object.keys(where).map(key => `a.${key} = ?`).join(' AND ') : ''}
    `;

    // 执行查询
    const data = await this.nativeQuery(sql, Object.values(where));

    // 如果指定了字段，则只返回指定字段
    if (fields && fields.length > 0) {
      return data.map(item => {
        const result = {};
        fields.forEach(field => {
          result[field] = item[field];
        });
        return result;
      });
    }

    return data;
  }
}
