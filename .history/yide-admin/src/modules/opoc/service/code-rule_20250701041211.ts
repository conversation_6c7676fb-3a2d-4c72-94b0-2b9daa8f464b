import { BaseService, CoolCommException } from '@cool-midway/core';
import { Provide } from '@midwayjs/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { OpocCodeRuleEntity } from '../entity/code-rule';

/**
 * 编码规则服务
 */
@Provide()
export class OpocCodeRuleService extends BaseService {
  @InjectEntityModel(OpocCodeRuleEntity)
  opocCodeRuleEntity: Repository<OpocCodeRuleEntity>;

  /**
   * 设置默认规则
   * @param id 规则ID
   */
  async setDefault(id: number) {
    // 查询规则是否存在
    const rule = await this.opocCodeRuleEntity.findOne({ where: { id } });
    if (!rule) {
      throw new CoolCommException('规则不存在');
    }

    // 将所有规则设置为非默认
    await this.opocCodeRuleEntity.update({}, { isDefault: false });

    // 将指定规则设置为默认
    await this.opocCodeRuleEntity.update(id, { isDefault: true });

    return true;
  }

  /**
   * 生成预览码
   * @param params 码格式参数
   */
  async generatePreview(params: {
    type: string;
    length: number;
    includeLetters?: boolean;
    includeNumbers?: boolean;
    includeSpecialChars?: boolean;
    prefix?: string;
  }) {
    let code = '';
    const prefix = params.prefix || '';
    
    // 根据类型生成不同的码
    switch (params.type) {
      case 'random':
        code = this.generateRandomCode(params.length);
        break;
      case 'number':
        code = this.generateNumberCode(params.length);
        break;
      case 'letter':
        code = this.generateLetterCode(params.length);
        break;
      case 'mixed':
        code = this.generateMixedCode(params.length);
        break;
      case 'custom':
        code = this.generateCustomCode(
          params.length,
          params.includeLetters || false,
          params.includeNumbers || false,
          params.includeSpecialChars || false
        );
        break;
      default:
        code = this.generateMixedCode(params.length);
    }
    
    return {
      code: prefix + code,
      format: params
    };
  }

  /**
   * 生成随机字符码
   */
  private generateRandomCode(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+';
    return this.generateCodeFromChars(chars, length);
  }

  /**
   * 生成数字码
   */
  private generateNumberCode(length: number): string {
    const chars = '0123456789';
    return this.generateCodeFromChars(chars, length);
  }

  /**
   * 生成字母码
   */
  private generateLetterCode(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    return this.generateCodeFromChars(chars, length);
  }

  /**
   * 生成混合码（字母+数字）
   */
  private generateMixedCode(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    return this.generateCodeFromChars(chars, length);
  }

  /**
   * 生成自定义码
   */
  private generateCustomCode(
    length: number,
    includeLetters: boolean,
    includeNumbers: boolean,
    includeSpecialChars: boolean
  ): string {
    let chars = '';
    
    if (includeLetters) {
      chars += 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    }
    
    if (includeNumbers) {
      chars += '0123456789';
    }
    
    if (includeSpecialChars) {
      chars += '!@#$%^&*()_+-=[]{}|;:,.<>?';
    }
    
    // 如果没有选择任何字符类型，默认使用字母+数字
    if (!chars) {
      chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    }
    
    return this.generateCodeFromChars(chars, length);
  }

  /**
   * 从字符集生成指定长度的随机码
   */
  private generateCodeFromChars(chars: string, length: number): string {
    let result = '';
    const charsLength = chars.length;
    
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * charsLength));
    }
    
    return result;
  }
}
