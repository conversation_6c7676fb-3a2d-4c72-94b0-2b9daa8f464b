import { BaseController, CoolController } from '@cool-midway/core';
import { Body, Inject, Post, Provide } from '@midwayjs/core';
import { OpocCodeRuleEntity } from '../../entity/code-rule';
import { OpocCodeRuleService } from '../../service/code-rule';

/**
 * 编码规则配置
 */
@Provide()
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: OpocCodeRuleEntity,
  service: OpocCodeRuleService,
  pageQueryOp: {
    keyWordLikeFields: ['name', 'type', 'prefix'],
    fieldEq: ['status', 'isDefault'],
  },
})
export class AdminOpocCodeRuleController extends BaseController {
  @Inject()
  opocCodeRuleService: OpocCodeRuleService;

  /**
   * 设置默认规则
   */
  @Post('/setDefault')
  async setDefault(@Body('id') id: number) {
    return this.ok(await this.opocCodeRuleService.setDefault(id));
  }

  /**
   * 生成预览码
   */
  @Post('/preview')
  async preview(@Body() params: {
    type: string;
    length: number;
    includeLetters?: boolean;
    includeNumbers?: boolean;
    includeSpecialChars?: boolean;
    prefix?: string;
  }) {
    return this.ok(await this.opocCodeRuleService.generatePreview(params));
  }

  /**
   * 生成唯一编码
   */
  @Post('/generateUnique')
  async generateUnique(@Body() params: {
    ruleId: number;
    count?: number;
  }) {
    const rule = await this.opocCodeRuleService.opocCodeRuleEntity.findOne({
      where: { id: params.ruleId }
    });
    if (!rule) {
      return this.fail('编码规则不存在');
    }

    const codes = await this.opocCodeRuleService.generateUniqueCode(rule, params.count || 1);
    return this.ok(codes);
  }

  /**
   * 生成高级编码
   */
  @Post('/generateAdvanced')
  async generateAdvanced(@Body() params: {
    ruleId: number;
    includeTimestamp?: boolean;
    timestampFormat?: 'YYYYMMDD' | 'YYMMDD' | 'YYYYMMDDHHMM';
    includeSequence?: boolean;
    sequenceLength?: number;
    includeChecksum?: boolean;
  }) {
    const rule = await this.opocCodeRuleService.opocCodeRuleEntity.findOne({
      where: { id: params.ruleId }
    });
    if (!rule) {
      return this.fail('编码规则不存在');
    }

    const code = await this.opocCodeRuleService.generateAdvancedCode(rule, params);
    return this.ok({ code });
  }

  /**
   * 验证编码格式
   */
  @Post('/validateFormat')
  async validateFormat(@Body() params: {
    code: string;
    ruleId: number;
  }) {
    const rule = await this.opocCodeRuleService.opocCodeRuleEntity.findOne({
      where: { id: params.ruleId }
    });
    if (!rule) {
      return this.fail('编码规则不存在');
    }

    const isValid = this.opocCodeRuleService.validateCodeFormat(params.code, rule);
    return this.ok({ isValid, rule: rule.name });
  }

  /**
   * 获取规则使用统计
   */
  @Post('/usageStats')
  async usageStats(@Body() params: { ruleId: number }) {
    const stats = await this.opocCodeRuleService.getRuleUsageStats(params.ruleId);
    return this.ok(stats);
  }

  /**
   * 获取默认规则
   */
  @Post('/getDefault')
  async getDefault() {
    const defaultRule = await this.opocCodeRuleService.getDefaultRule();
    return this.ok(defaultRule);
  }
}
