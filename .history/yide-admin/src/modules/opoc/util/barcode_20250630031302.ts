import { Provide } from '@midwayjs/core';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 条形码选项接口
 */
interface BarcodeOptions {
  format?: string;
  width?: number;
  height?: number;
  displayValue?: boolean;
  text?: string;
  fontOptions?: string;
  font?: string;
  textAlign?: string;
  textPosition?: string;
  textMargin?: number;
  fontSize?: number;
  background?: string;
  lineColor?: string;
  margin?: number;
  marginTop?: number;
  marginBottom?: number;
  marginLeft?: number;
  marginRight?: number;
  valid?: (valid: boolean) => void;
}

/**
 * 条形码生成工具类
 */
@Provide()
export class BarcodeUtil {
  /**
   * 生成条形码
   * @param text 文本内容
   * @param options 选项
   * @returns 条形码图片的 base64 字符串
   */
  generate(text: string, options?: BarcodeOptions): string {
    try {
      // 临时实现：生成SVG格式的条形码
      const width = options?.width || 2;
      const height = options?.height || 100;
      const background = options?.background || '#ffffff';
      const lineColor = options?.lineColor || '#000000';
      const displayValue = options?.displayValue !== false;

      // 简单的CODE128编码模拟（实际应该使用真正的编码算法）
      const bars = this.generateSimpleBars(text);

      // 生成SVG
      const svgWidth = bars.length * width + 20; // 加上边距
      const svgHeight = height + (displayValue ? 30 : 10); // 加上文字高度

      let svg = `<svg width="${svgWidth}" height="${svgHeight}" xmlns="http://www.w3.org/2000/svg">`;
      svg += `<rect width="100%" height="100%" fill="${background}"/>`;

      // 绘制条形码
      let x = 10; // 左边距
      for (let i = 0; i < bars.length; i++) {
        if (bars[i] === '1') {
          svg += `<rect x="${x}" y="5" width="${width}" height="${height}" fill="${lineColor}"/>`;
        }
        x += width;
      }

      // 添加文字
      if (displayValue) {
        const textX = svgWidth / 2;
        const textY = height + 20;
        svg += `<text x="${textX}" y="${textY}" text-anchor="middle" font-family="monospace" font-size="14" fill="${lineColor}">${text}</text>`;
      }

      svg += '</svg>';

      // 转换为base64
      const base64 = Buffer.from(svg).toString('base64');
      return `data:image/svg+xml;base64,${base64}`;
    } catch (error) {
      console.error('生成条形码失败:', error);
      throw new Error('生成条形码失败');
    }
  }

  /**
   * 生成简单的条形码模式（临时实现）
   * @param text 文本内容
   * @returns 条形码模式字符串
   */
  private generateSimpleBars(text: string): string {
    // 这是一个简化的实现，实际应该使用真正的CODE128编码
    let bars = '11010010000'; // 起始码

    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      // 简单的字符到条形码映射
      const pattern = this.getCharPattern(char);
      bars += pattern;
    }

    bars += '1100011101011'; // 结束码
    return bars;
  }

  /**
   * 获取字符对应的条形码模式
   * @param charCode 字符编码
   * @returns 条形码模式
   */
  private getCharPattern(charCode: number): string {
    // 简化的字符映射表
    const patterns = [
      '11011001100', '11001101100', '11001100110', '10010011000',
      '10010001100', '10001001100', '10011001000', '10011000100',
      '10001100100', '11001001000', '11001000100', '11000100100'
    ];

    return patterns[charCode % patterns.length];
  }

  /**
   * 生成条形码并返回 Buffer
   * @param text 文本内容
   * @param options 选项
   * @returns 条形码图片的 Buffer
   */
  generateBuffer(text: string, options?: BarcodeOptions): Buffer {
    try {
      // 创建Canvas
      const canvas = createCanvas(300, 100);

      // 默认选项
      const defaultOptions: JsBarcode.Options = {
        format: 'CODE128',
        width: 2,
        height: 100,
        displayValue: true,
        text: text, // 显示的文本
        fontOptions: '',
        font: 'monospace',
        textAlign: 'center',
        textPosition: 'bottom',
        textMargin: 2,
        fontSize: 20,
        background: '#ffffff',
        lineColor: '#000000',
        margin: 10,
        marginTop: undefined,
        marginBottom: undefined,
        marginLeft: undefined,
        marginRight: undefined,
        valid: function(valid) {
          if (!valid) {
            throw new Error('条形码内容无效');
          }
        }
      };

      // 合并选项
      const mergedOptions = { ...defaultOptions, ...options };

      // 生成条形码
      JsBarcode(canvas, text, mergedOptions);

      // 返回buffer
      return canvas.toBuffer('image/png');
    } catch (error) {
      console.error('生成条形码失败:', error);
      throw new Error('生成条形码失败');
    }
  }

  /**
   * 生成条形码并保存到文件
   * @param text 文本内容
   * @param filePath 文件保存路径
   * @param options 选项
   * @returns 保存的文件路径
   */
  generateToFile(text: string, filePath: string, options?: BarcodeOptions): string {
    try {
      // 确保目录存在
      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      // 生成条形码Buffer
      const buffer = this.generateBuffer(text, options);

      // 写入文件
      fs.writeFileSync(filePath, buffer);

      return filePath;
    } catch (error) {
      console.error('生成条形码并保存到文件失败:', error);
      throw new Error('生成条形码并保存到文件失败');
    }
  }

  /**
   * 批量生成条形码
   * @param textList 文本内容列表
   * @param options 选项
   * @returns 条形码图片的 base64 字符串列表
   */
  batchGenerate(textList: string[], options?: BarcodeOptions): string[] {
    try {
      const results: string[] = [];
      for (const text of textList) {
        const barcode = this.generate(text, options);
        results.push(barcode);
      }
      return results;
    } catch (error) {
      console.error('批量生成条形码失败:', error);
      throw new Error('批量生成条形码失败');
    }
  }

  /**
   * 批量生成条形码并保存到文件
   * @param textList 文本内容列表
   * @param dirPath 保存目录路径
   * @param fileNamePrefix 文件名前缀
   * @param options 选项
   * @returns 保存的文件路径列表
   */
  batchGenerateToFile(
    textList: string[],
    dirPath: string,
    fileNamePrefix: string = 'barcode',
    options?: BarcodeOptions
  ): string[] {
    try {
      // 确保目录存在
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
      }

      const filePaths: string[] = [];
      for (let i = 0; i < textList.length; i++) {
        const filePath = path.join(dirPath, `${fileNamePrefix}_${i + 1}.png`);
        this.generateToFile(textList[i], filePath, options);
        filePaths.push(filePath);
      }
      return filePaths;
    } catch (error) {
      console.error('批量生成条形码并保存到文件失败:', error);
      throw new Error('批量生成条形码并保存到文件失败');
    }
  }

  /**
   * 生成EAN-13条形码
   * @param text 13位数字
   * @param options 选项
   * @returns 条形码图片的 base64 字符串
   */
  generateEAN13(text: string, options?: BarcodeOptions): string {
    // 验证输入是否为13位数字
    if (!/^\d{13}$/.test(text)) {
      throw new Error('EAN-13条形码必须是13位数字');
    }

    const ean13Options: BarcodeOptions = {
      ...options,
      format: 'EAN13'
    };

    return this.generate(text, ean13Options);
  }

  /**
   * 生成UPC-A条形码
   * @param text 12位数字
   * @param options 选项
   * @returns 条形码图片的 base64 字符串
   */
  generateUPCA(text: string, options?: BarcodeOptions): string {
    // 验证输入是否为12位数字
    if (!/^\d{12}$/.test(text)) {
      throw new Error('UPC-A条形码必须是12位数字');
    }

    const upcaOptions: BarcodeOptions = {
      ...options,
      format: 'UPC'
    };

    return this.generate(text, upcaOptions);
  }

  /**
   * 生成CODE39条形码
   * @param text 文本内容
   * @param options 选项
   * @returns 条形码图片的 base64 字符串
   */
  generateCODE39(text: string, options?: BarcodeOptions): string {
    const code39Options: BarcodeOptions = {
      ...options,
      format: 'CODE39'
    };

    return this.generate(text, code39Options);
  }
}
