import { BaseEntity } from '@cool-midway/core';
import { Column, Entity } from 'typeorm';

/**
 * 产品码
 */
@Entity('opoc_product_code')
export class OpocProductCodeEntity extends BaseEntity {
  @Column({ comment: '标识码', length: 255 })
  code: string;

  @Column({ comment: '产品ID', nullable: true })
  productId: number;

  @Column({
    comment: '标识码状态（0:未使用, 1:已绑定, 2:已激活, 3:已失效）',
    default: 0,
  })
  status: number;

  @Column({ comment: '激活时间', type: 'datetime', nullable: true })
  activateTime: Date;

  @Column({ comment: '验证次数', default: 0 })
  verifyCount: number;

  @Column({ comment: '验证次数限制', default: 0, nullable: true })
  verifyLimit: number;

  @Column({ comment: '最后验证时间', type: 'datetime', nullable: true })
  lastVerifyTime: Date;
}
